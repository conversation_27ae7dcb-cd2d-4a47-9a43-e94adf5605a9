# Test Configuration
spring.profiles.active=test

# Allow bean definition overriding for test configuration
spring.main.allow-bean-definition-overriding=true

# H2 In-Memory Database Configuration for Tests
spring.datasource.url=jdbc:h2:mem:primary-test-db;DB_CLOSE_DELAY=-1;MODE=PostgreSQL
spring.datasource.username=sa
spring.datasource.password=
spring.datasource.driver-class-name=org.h2.Driver
spring.datasource.jdbc-url=${spring.datasource.url}

# H2 In-Memory DMT Database Configuration for Tests
spring.datasource.dmt.url=jdbc:h2:mem:dmt-test-db;DB_CLOSE_DELAY=-1;MODE=PostgreSQL
spring.datasource.dmt.username=sa
spring.datasource.dmt.password=
spring.datasource.dmt.driver-class-name=org.h2.Driver
spring.datasource.dmt.jdbc-url=${spring.datasource.dmt.url}

# JPA Configuration for Tests
# Use create-drop for tests to automatically create schema
spring.jpa.hibernate.ddl-auto=create-drop
spring.jpa.database-platform=org.hibernate.dialect.H2Dialect
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true
# spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect
spring.jpa.properties.hibernate.physical_naming_strategy=org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl
spring.jpa.properties.hibernate.implicit_naming_strategy=org.hibernate.boot.model.naming.ImplicitNamingStrategyJpaCompliantImpl

# Server Configuration
server.port=0

# JWT Configuration
jwt.secret=TestSecretKeyForJWTThatIsLongEnoughToBeSecureInTestEnvironment
jwt.expiration=86400000

# Email Configuration - Disabled for tests
spring.mail.host=smtp.gmail.com
spring.mail.port=587
spring.mail.username=<EMAIL>
spring.mail.password=test-password
spring.mail.properties.mail.smtp.auth=false
spring.mail.properties.mail.smtp.starttls.enable=false

# Session Configuration - Disabled for tests
spring.session.store-type=none

spring.main.banner-mode=off
logging.level.root=WARN
logging.level.org.springframework.web=WARN
logging.level.org.hibernate.SQL=WARN
logging.level.org.hibernate.type.descriptor.sql=WARN
logging.level.com.quotaapp.backend=INFO
{"name": "quota.app", "version": "1.0.0", "private": true, "workspaces": ["frontend-vehicle", "frontend-station", "frontend-admin"], "scripts": {"dev:vehicle": "cd frontend-vehicle && bun run dev", "dev:station": "cd frontend-station && bun run dev", "dev:admin": "cd frontend-admin && bun run dev", "dev:mobile": "cd mobile-app/quota.app && bun run start", "dev:all": "concurrently \"bun run dev:vehicle\" \"bun run dev:station\" \"bun run dev:admin\" \"bun run dev:mobile\""}, "devDependencies": {"concurrently": "^9.1.2"}, "dependencies": {"@radix-ui/react-collection": "^1.1.6", "@radix-ui/react-compose-refs": "^1.1.2", "@radix-ui/react-context": "^1.1.2", "@radix-ui/react-menu": "^2.1.14", "@radix-ui/react-primitive": "^2.1.2", "@radix-ui/react-roving-focus": "^1.1.9", "@radix-ui/react-slot": "^1.2.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "tailwind-merge": "^3.2.0"}}